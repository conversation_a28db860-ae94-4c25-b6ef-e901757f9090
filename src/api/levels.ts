import { apiClient } from '@/lib/api-client';
import { logActivity } from '../utils/activity-logger';
import { toast } from 'sonner';

// Level type definition
export interface Level {
  id: string;
  name: string;
  code?: string;
  description?: string;
  course_id?: string;
  course?: { name: string };
  students?: Array<{ id: string; name: string }>; // Optional students array
  created_at?: string;
  updated_at?: string;
}

export const getLevels = async () => {
  try {
    console.log('Fetching levels from API');
    const levels = await apiClient.get<Level[]>('/levels');
    return levels;
  } catch (error) {
    console.error('Error fetching levels:', error);
    throw error;
  }
};

export const createLevel = async (data: Omit<Level, 'id' | 'created_at' | 'updated_at'>) => {
  try {
    console.log('Creating new level:', data);
    const newLevel = await apiClient.post<Level>('/levels', data);

    await logActivity('level_created', { levelId: newLevel.id });

    toast.success('Level created successfully');
    return newLevel;
  } catch (error: any) {
    console.error('Error creating level:', error);
    toast.error(error.message || 'Failed to create level');
    throw error;
  }
};

export const updateLevel = async (id: string, data: Partial<Level>) => {
  try {
    await apiClient.put(`/levels/${id}`, data);

    await logActivity('level_updated', { levelId: id });

    toast.success('Level updated successfully');
    return true;
  } catch (error: any) {
    console.error('Error updating level:', error);
    toast.error(error.message || 'Failed to update level');
    throw error;
  }
};

export const deleteLevel = async (id: string) => {
  try {
    const level = await apiClient.get<Level>(`/levels/${id}`);

    await apiClient.delete(`/levels/${id}`);

    await logActivity('level_deleted', { levelId: id });

    toast.success(`Successfully deleted level ${level.name} (${level.code})`);
    return true;
  } catch (error: any) {
    console.error('Error deleting level:', error);
    toast.error(error.message || 'Failed to delete level');
    throw error;
  }
};
