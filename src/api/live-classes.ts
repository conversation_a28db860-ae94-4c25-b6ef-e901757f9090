import { apiClient } from '@/lib/api-client';

// Live class types - to be defined based on PHP backend structure
interface LiveClass {
  id: string;
  title: string;
  description?: string;
  date: string;
  time: string;
  status: string;
}

interface LiveClassInput {
  title: string;
  description?: string;
  date: string;
  time: string;
}

interface Comment {
  id: string;
  content: string;
  author: string;
  created_at: string;
}

interface CommentInput {
  content: string;
  live_class_id: string;
}

export const createLiveClass = async (data: Omit<LiveClassInput, 'teacherId'>): Promise<LiveClass> => {
  try {
    const { apiClient } = await import('@/lib/api-client');

    const liveClass = await apiClient.post<LiveClass>('/live-classes', data);
    return liveClass;
  } catch (error) {
    console.error('Error creating live class:', error);
    throw error;
  }
};

export const updateLiveClass = async (id: string, data: Partial<LiveClassInput>): Promise<void> => {
  try {
    await apiClient.put(`/live-classes/${id}`, data);
  } catch (error) {
    console.error('Error updating live class:', error);
    throw error;
  }
};

export const deleteLiveClass = async (id: string): Promise<void> => {
  try {
    await apiClient.delete(`/live-classes/${id}`);
  } catch (error) {
    console.error('Error deleting live class:', error);
    throw error;
  }
};

export const getLiveClasses = async (options?: {
  teacherId?: string;
  courseId?: string;
  levelId?: string;
  active?: boolean;
}): Promise<LiveClass[]> => {
  try {
    const params = new URLSearchParams();
    if (options?.teacherId) params.append('teacher_id', options.teacherId);
    if (options?.levelId) params.append('level_id', options.levelId);
    if (options?.active !== undefined) params.append('active', options.active.toString());

    return await apiClient.get<LiveClass[]>(`/live-classes?${params.toString()}`);
  } catch (error) {
    console.error('Error fetching live classes:', error);
    throw error;
  }
};

export const getActiveLiveClasses = async (options?: {
  teacherId?: string;
  courseId?: string;
  levelId?: string;
}): Promise<LiveClass[]> => {
  try {
    return await getLiveClasses({
      ...options,
      active: true
    });
  } catch (error) {
    console.error('Error fetching active live classes:', error);
    throw error;
  }
};

export const subscribeLiveClasses = (
  options: {
    teacherId?: string;
    courseId?: string;
    levelId?: string;
    active?: boolean;
  },
  callback: (liveClasses: LiveClass[]) => void
) => {
  // For now, return a simple polling mechanism
  // In a real implementation, you might use WebSockets or Server-Sent Events
  const interval = setInterval(async () => {
    try {
      const liveClasses = await getLiveClasses(options);
      callback(liveClasses);
    } catch (error) {
      console.error('Error in live classes subscription:', error);
    }
  }, 10000); // Poll every 10 seconds

  return () => clearInterval(interval);
};

// Comment functions
export const createComment = async (
  liveClassId: string,
  content: string
): Promise<Comment> => {
  try {
    // Use custom auth service instead of Firebase
    const { AuthService } = await import('@/lib/auth');
    const user = await AuthService.getCurrentUser();

    if (!user) {
      throw new Error('User not authenticated');
    }

    const commentData = {
      content,
      live_class_id: liveClassId
    };

    return await apiClient.post<Comment>('/comments', commentData);
  } catch (error) {
    console.error('Error creating comment:', error);
    throw error;
  }
};

export const updateComment = async (id: string, content: string): Promise<void> => {
  try {
    await apiClient.put(`/comments/${id}`, { content });
  } catch (error) {
    console.error('Error updating comment:', error);
    throw error;
  }
};

export const deleteComment = async (id: string): Promise<void> => {
  try {
    await apiClient.delete(`/comments/${id}`);
  } catch (error) {
    console.error('Error deleting comment:', error);
    throw error;
  }
};

export const hardDeleteComment = async (id: string): Promise<void> => {
  try {
    await apiClient.delete(`/comments/${id}?hard=true`);
  } catch (error) {
    console.error('Error hard deleting comment:', error);
    throw error;
  }
};

export const getComments = async (
  liveClassId: string,
  options: {
    limit?: number;
    includeDeleted?: boolean;
  } = {}
): Promise<Comment[]> => {
  try {
    const params = new URLSearchParams();
    if (options.limit) params.append('limit', options.limit.toString());
    if (options.includeDeleted) params.append('include_deleted', 'true');

    return await apiClient.get<Comment[]>(`/live-classes/${liveClassId}/comments?${params.toString()}`);
  } catch (error) {
    console.error('Error fetching comments:', error);
    throw error;
  }
};

export const subscribeComments = (
  liveClassId: string,
  options: {
    limit?: number;
    includeDeleted?: boolean;
  } = {},
  callback: (comments: Comment[]) => void
) => {
  // For now, return a simple polling mechanism
  // In a real implementation, you might use WebSockets or Server-Sent Events
  const interval = setInterval(async () => {
    try {
      const comments = await getComments(liveClassId, options);
      callback(comments);
    } catch (error) {
      console.error('Error in comment subscription:', error);
    }
  }, 5000); // Poll every 5 seconds

  return () => clearInterval(interval);
};