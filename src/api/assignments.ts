import { apiClient } from '@/lib/api-client';
import { logActivity } from '../utils/activity-logger';
import { toast } from 'sonner';

export interface Assignment {
  id: string;
  title: string;
  description: string;
  course_id: string;
  level_id: string;
  due_date: string;
  status: string;
  points?: number;
  created_at?: string;
  updated_at?: string;
}

export type AssignmentFormData = {
  title: string;
  description: string;
  course_id: string;
  level_id: string;
  due_date: string;
  points?: number;
  status?: string;
};

export const getAssignments = async (): Promise<Assignment[]> => {
  try {
    console.log('Fetching assignments from API');
    const assignments = await apiClient.get<Assignment[]>('/assignments');
    
    // Sort assignments by due date (closest first)
    return assignments.sort((a, b) => new Date(a.due_date).getTime() - new Date(b.due_date).getTime());
  } catch (error) {
    console.error('Error fetching assignments:', error);
    throw error;
  }
};

export const getAssignmentById = async (id: string): Promise<Assignment | null> => {
  try {
    console.log(`Fetching assignment with ID: ${id}`);
    const assignment = await apiClient.get<Assignment>(`/assignments/${id}`);
    return assignment;
  } catch (error) {
    console.error('Error fetching assignment:', error);
    return null;
  }
};

export const getAssignmentsByLevel = async (levelId: string): Promise<Assignment[]> => {
  try {
    console.log(`Fetching assignments for level: ${levelId}`);
    const assignments = await apiClient.get<Assignment[]>(`/levels/${levelId}/assignments`);
    
    // Sort assignments by due date (closest first)
    return assignments.sort((a, b) => new Date(a.due_date).getTime() - new Date(b.due_date).getTime());
  } catch (error) {
    console.error('Error fetching assignments by level:', error);
    throw error;
  }
};

export const createAssignment = async (assignmentData: AssignmentFormData): Promise<Assignment> => {
  try {
    console.log('Creating new assignment:', assignmentData);
    
    // Set default status if not provided
    const status = assignmentData.status || 'active';
    
    const newAssignment = await apiClient.post<Assignment>('/assignments', {
      ...assignmentData,
      status
    });
    
    await logActivity('settings_updated', {
      type: 'assignment',
      action: 'created',
      id: newAssignment.id,
      title: assignmentData.title,
      levelId: assignmentData.level_id
    });
    
    toast.success('Assignment created successfully');
    return newAssignment;
  } catch (error: any) {
    console.error('Error creating assignment:', error);
    toast.error(error.message || 'Failed to create assignment');
    throw error;
  }
};

export const updateAssignment = async (id: string, assignmentData: Partial<AssignmentFormData>): Promise<boolean> => {
  try {
    console.log('Updating assignment:', id, assignmentData);
    
    await apiClient.put(`/assignments/${id}`, assignmentData);
    
    await logActivity('settings_updated', {
      type: 'assignment',
      action: 'updated',
      id: id,
      updates: assignmentData
    });
    
    toast.success('Assignment updated successfully');
    return true;
  } catch (error: any) {
    console.error('Error updating assignment:', error);
    toast.error(error.message || 'Failed to update assignment');
    throw error;
  }
};

export const deleteAssignment = async (id: string): Promise<boolean> => {
  try {
    console.log('Deleting assignment:', id);
    
    // Get assignment details before deletion for logging
    const assignment = await getAssignmentById(id);
    
    await apiClient.delete(`/assignments/${id}`);
    
    await logActivity('settings_updated', {
      type: 'assignment',
      action: 'deleted',
      id: id,
      title: assignment?.title || 'Unknown'
    });
    
    toast.success('Assignment deleted successfully');
    return true;
  } catch (error: any) {
    console.error('Error deleting assignment:', error);
    toast.error(error.message || 'Failed to delete assignment');
    throw error;
  }
};

export const getStudentAssignments = async (studentId: string): Promise<Assignment[]> => {
  try {
    console.log(`Fetching assignments for student: ${studentId}`);
    const assignments = await apiClient.get<Assignment[]>(`/students/${studentId}/assignments`);

    // Sort assignments by due date (closest first)
    return assignments.sort((a, b) => new Date(a.due_date).getTime() - new Date(b.due_date).getTime());
  } catch (error) {
    console.error('Error fetching student assignments:', error);
    throw error;
  }
};

// Assignment submission interface
export interface AssignmentSubmission {
  id: string;
  assignment_id: string;
  student_id: string;
  student_name: string;
  submitted_at: string;
  content?: string;
  file_url?: string;
  grade?: number;
  feedback?: string;
  status: 'submitted' | 'graded' | 'late';
}

// Get assignment submissions
export const getAssignmentSubmissions = async (assignmentId: string): Promise<AssignmentSubmission[]> => {
  try {
    console.log(`Fetching submissions for assignment: ${assignmentId}`);
    const submissions = await apiClient.get<AssignmentSubmission[]>(`/assignments/${assignmentId}/submissions`);

    // Sort submissions by submission date (newest first)
    return submissions.sort((a, b) => new Date(b.submitted_at).getTime() - new Date(a.submitted_at).getTime());
  } catch (error) {
    console.error('Error fetching assignment submissions:', error);
    throw error;
  }
};

// Grade submission interface
export interface GradeSubmissionData {
  grade: number;
  feedback?: string;
}

// Grade a submission
export const gradeSubmission = async (submissionId: string, gradeData: GradeSubmissionData): Promise<boolean> => {
  try {
    console.log('Grading submission:', submissionId, gradeData);

    await apiClient.put(`/submissions/${submissionId}/grade`, gradeData);

    await logActivity('settings_updated', {
      type: 'assignment_grade',
      submissionId,
      grade: gradeData.grade,
      hasFeedback: !!gradeData.feedback
    });

    toast.success('Submission graded successfully');
    return true;
  } catch (error: any) {
    console.error('Error grading submission:', error);
    toast.error(error.message || 'Failed to grade submission');
    throw error;
  }
};
