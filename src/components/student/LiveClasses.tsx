import { useState, useEffect, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  Calendar,
  Clock,
  Edit,
  Loader2,
  MessageSquare,
  MoreHorizontal,
  Send,
  Trash2,
  Video,
} from 'lucide-react';
import { format } from 'date-fns';
import { useToast } from '@/components/ui/use-toast';
import {
  getActiveLiveClasses,
  createComment,
  updateComment,
  deleteComment,
} from '@/api/live-classes';
// Live class types - enhanced for student component
interface LiveClass {
  id: string;
  title: string;
  description?: string;
  date: string;
  time: string;
  status: string;
  // Additional properties that may come from API or be computed
  startTime?: Date;
  endTime?: Date;
  meetingLink?: string;
}

interface Comment {
  id: string;
  content: string;
  author: string;
  created_at: string;
  // Additional properties for enhanced functionality
  userName?: string;
  userRole?: string;
  userId?: string;
  createdAt?: Date;
}
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';

export function LiveClasses() {
  const { user } = useAuth();
  const [liveClasses, setLiveClasses] = useState<LiveClass[]>([]);
  const [selectedClass, setSelectedClass] = useState<LiveClass | null>(null);
  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isPostingComment, setIsPostingComment] = useState(false);
  const [editingCommentId, setEditingCommentId] = useState<string | null>(null);
  const [editingCommentText, setEditingCommentText] = useState('');
  const commentsEndRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  // Fetch live classes on mount
  useEffect(() => {
    const fetchLiveClasses = async () => {
      setIsLoading(true);
      try {
        const classes = await getActiveLiveClasses();
        setLiveClasses(classes);
        if (classes.length > 0) {
          setSelectedClass(classes[0]);
        }
      } catch (error) {
        console.error('Error fetching live classes:', error);
        toast({
          variant: 'destructive',
          title: 'Failed to load live classes',
          description: 'Please try again later',
        });
      } finally {
        setIsLoading(false);
      }
    };

    // Set up real-time subscription (simplified for now)
    // TODO: Implement real-time subscription when backend supports it
    const unsubscribe = () => {
      // Placeholder unsubscribe function
    };

    fetchLiveClasses();

    return () => {
      unsubscribe();
    };
  }, [toast]);

  // Subscribe to comments when selected class changes
  useEffect(() => {
    if (!selectedClass) return;

    // TODO: Implement comment fetching when backend supports it
    // For now, initialize with empty comments
    setComments([]);

    // Placeholder cleanup function
    return () => {
      // Cleanup if needed
    };
  }, [selectedClass]);

  // Scroll to bottom when new comments are added
  useEffect(() => {
    commentsEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [comments]);

  const handleSelectClass = (liveClass: LiveClass) => {
    setSelectedClass(liveClass);
  };

  const handlePostComment = async () => {
    if (!selectedClass || !newComment.trim() || !user) return;

    setIsPostingComment(true);
    try {
      await createComment(
        selectedClass.id,
        newComment,
        user.email || 'Anonymous Student',
        'student'
      );
      setNewComment('');
    } catch (error) {
      console.error('Error posting comment:', error);
      toast({
        variant: 'destructive',
        title: 'Failed to post comment',
        description: 'Please try again later',
      });
    } finally {
      setIsPostingComment(false);
    }
  };

  const handleEditComment = (comment: Comment) => {
    setEditingCommentId(comment.id);
    setEditingCommentText(comment.content);
  };

  const handleUpdateComment = async () => {
    if (!editingCommentId || !editingCommentText.trim()) return;

    try {
      await updateComment(editingCommentId, editingCommentText);
      setEditingCommentId(null);
      setEditingCommentText('');
      toast({
        title: 'Comment updated',
        description: 'Your comment has been updated successfully',
      });
    } catch (error) {
      console.error('Error updating comment:', error);
      toast({
        variant: 'destructive',
        title: 'Failed to update comment',
        description: 'Please try again later',
      });
    }
  };

  const handleDeleteComment = async (commentId: string) => {
    try {
      await deleteComment(commentId);
      toast({
        title: 'Comment deleted',
        description: 'Your comment has been deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting comment:', error);
      toast({
        variant: 'destructive',
        title: 'Failed to delete comment',
        description: 'Please try again later',
      });
    }
  };

  // Format date for display - handle both Date objects and date strings
  const formatDate = (date: Date | string | undefined) => {
    if (!date) return 'Date TBA';
    try {
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      return format(dateObj, 'PPP');
    } catch (error) {
      return 'Date TBA';
    }
  };

  // Format time for display - handle both Date objects and time strings
  const formatTime = (date: Date | string | undefined) => {
    if (!date) return 'Time TBA';
    try {
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      return format(dateObj, 'h:mm a');
    } catch (error) {
      return 'Time TBA';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold tracking-tight">Live Classes</h2>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      ) : liveClasses.length === 0 ? (
        <Card>
          <CardHeader>
            <CardTitle>No Live Classes Available</CardTitle>
            <CardDescription>
              There are no live classes scheduled at the moment. Please check back later.
            </CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col items-center py-8">
            <Video className="h-16 w-16 text-muted-foreground mb-4" />
            <p className="text-center text-muted-foreground">
              Your teachers will schedule live classes that will appear here.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle>Upcoming Classes</CardTitle>
                <CardDescription>
                  Select a class to join or view details
                </CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <ul className="divide-y">
                  {liveClasses.map((liveClass) => (
                    <li key={liveClass.id}>
                      <Button
                        variant="ghost"
                        className={`w-full justify-start p-4 rounded-none ${
                          selectedClass?.id === liveClass.id
                            ? 'bg-green-50 text-green-700'
                            : 'text-gray-700'
                        }`}
                        onClick={() => handleSelectClass(liveClass)}
                      >
                        <div className="flex flex-col items-start">
                          <span className="font-medium">{liveClass.title}</span>
                          <span className="text-xs text-muted-foreground">
                            {formatDate(liveClass.startTime)} • {formatTime(liveClass.startTime)}
                          </span>
                        </div>
                      </Button>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </div>

          <div className="md:col-span-2">
            {selectedClass && (
              <Card className="h-full flex flex-col">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle>{selectedClass.title}</CardTitle>
                      <CardDescription className="mt-1">
                        {formatDate(selectedClass.startTime)} • {formatTime(selectedClass.startTime)} - {formatTime(selectedClass.endTime)}
                      </CardDescription>
                    </div>
                    <Button
                      asChild
                      className="bg-blue-600 hover:bg-blue-700"
                      disabled={!selectedClass.meetingLink}
                    >
                      <a
                        href={selectedClass.meetingLink || '#'}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <Video className="mr-2 h-4 w-4" />
                        {selectedClass.meetingLink ? 'Join Meeting' : 'Meeting Link TBA'}
                      </a>
                    </Button>
                  </div>
                </CardHeader>
                
                <CardContent className="flex-grow">
                  <Tabs defaultValue="details">
                    <TabsList className="grid grid-cols-2 mb-4">
                      <TabsTrigger value="details">Details</TabsTrigger>
                      <TabsTrigger value="comments">Comments</TabsTrigger>
                    </TabsList>
                    <TabsContent value="details">
                      <div className="prose max-w-none">
                        <p>{selectedClass.description}</p>
                      </div>
                      
                      <div className="mt-6 space-y-4">
                        <div className="flex items-center gap-2 text-sm">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span>
                            {formatDate(selectedClass.startTime)}
                          </span>
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          <span>
                            {formatTime(selectedClass.startTime)} - {formatTime(selectedClass.endTime)}
                          </span>
                        </div>
                      </div>
                      
                      <div className="mt-8">
                        <h4 className="text-sm font-medium mb-2">Joining Instructions</h4>
                        <ol className="list-decimal list-inside space-y-2 text-sm text-muted-foreground">
                          <li>Click the "Join Meeting" button above</li>
                          <li>Allow your browser to open Zoom if prompted</li>
                          <li>Enter your full name when joining</li>
                          <li>Make sure your camera and microphone are working</li>
                          <li>Be ready 5 minutes before the session starts</li>
                        </ol>
                      </div>
                    </TabsContent>
                    
                    <TabsContent value="comments" className="h-[400px] flex flex-col">
                      <div className="flex-grow overflow-y-auto mb-4 space-y-4">
                        {comments.length === 0 ? (
                          <div className="flex flex-col items-center justify-center h-full text-center p-4">
                            <MessageSquare className="h-12 w-12 text-muted-foreground mb-4" />
                            <h3 className="font-medium">No comments yet</h3>
                            <p className="text-sm text-muted-foreground mt-1">
                              Be the first to start the conversation
                            </p>
                          </div>
                        ) : (
                          <>
                            {comments.map((comment) => (
                              <div key={comment.id} className="relative group">
                                {editingCommentId === comment.id ? (
                                  <div className="space-y-2">
                                    <Textarea
                                      value={editingCommentText}
                                      onChange={(e) => setEditingCommentText(e.target.value)}
                                      className="w-full"
                                    />
                                    <div className="flex space-x-2 justify-end">
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => {
                                          setEditingCommentId(null);
                                          setEditingCommentText('');
                                        }}
                                      >
                                        Cancel
                                      </Button>
                                      <Button
                                        size="sm"
                                        onClick={handleUpdateComment}
                                      >
                                        Save
                                      </Button>
                                    </div>
                                  </div>
                                ) : (
                                  <div className="bg-muted/50 p-3 rounded-lg">
                                    <div className="flex justify-between items-start">
                                      <div className="flex items-center gap-2">
                                        <span className="font-medium text-sm">
                                          {comment.userName || comment.author}
                                        </span>
                                        <Badge
                                          variant={comment.userRole === 'teacher' ? 'default' : 'outline'}
                                          className="text-xs"
                                        >
                                          {comment.userRole === 'teacher' ? 'Teacher' : 'Student'}
                                        </Badge>
                                        <span className="text-xs text-muted-foreground">
                                          {comment.createdAt ? format(comment.createdAt, 'h:mm a') :
                                           comment.created_at ? format(new Date(comment.created_at), 'h:mm a') : 'Time TBA'}
                                        </span>
                                      </div>

                                      {user && comment.userId === user.id && (
                                        <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                                          <DropdownMenu>
                                            <DropdownMenuTrigger asChild>
                                              <Button variant="ghost" size="icon" className="h-8 w-8">
                                                <MoreHorizontal className="h-4 w-4" />
                                                <span className="sr-only">More options</span>
                                              </Button>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent align="end">
                                              <DropdownMenuItem onClick={() => handleEditComment(comment)}>
                                                <Edit className="mr-2 h-4 w-4" />
                                                Edit
                                              </DropdownMenuItem>
                                              <DropdownMenuSeparator />
                                              <DropdownMenuItem
                                                onClick={() => handleDeleteComment(comment.id)}
                                                className="text-red-500 focus:text-red-500"
                                              >
                                                <Trash2 className="mr-2 h-4 w-4" />
                                                Delete
                                              </DropdownMenuItem>
                                            </DropdownMenuContent>
                                          </DropdownMenu>
                                        </div>
                                      )}
                                    </div>
                                    <p className="mt-1 text-sm">{comment.content}</p>
                                  </div>
                                )}
                              </div>
                            ))}
                            <div ref={commentsEndRef} />
                          </>
                        )}
                      </div>
                      
                      <div className="mt-auto">
                        <Separator className="my-4" />
                        <div className="flex items-center space-x-2">
                          <Input
                            value={newComment}
                            onChange={(e) => setNewComment(e.target.value)}
                            placeholder="Type your comment..."
                            onKeyDown={(e) => {
                              if (e.key === 'Enter' && !e.shiftKey) {
                                e.preventDefault();
                                handlePostComment();
                              }
                            }}
                          />
                          <Button 
                            onClick={handlePostComment}
                            disabled={!newComment.trim() || isPostingComment}
                          >
                            {isPostingComment ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Send className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </div>
                    </TabsContent>
                  </Tabs>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

export default LiveClasses;