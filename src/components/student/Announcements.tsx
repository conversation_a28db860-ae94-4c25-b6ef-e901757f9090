import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Bell,
  Calendar,
  Search,
  Megaphone,
  AlertCircle,
  Info,
  BookOpen,
  Users,
  Globe,
  Loader2
} from "lucide-react";
import { Student } from "@/types/student";
import { useQuery } from "@tanstack/react-query";
import { getAnnouncementsByLevel, getAnnouncements, Announcement } from "@/api/announcements";
// Timestamp handling now done with regular Date objects

interface AnnouncementsProps {
  student: Student | null;
}



const Announcements = ({ student }: AnnouncementsProps) => {
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [filter, setFilter] = useState<string>("all");
  
  // Get the student's level ID, handling different data structures
  const getStudentLevelId = () => {
    if (student?.level?.id) {
      return student.level.id;
    } else if (student?.level_id) {
      return student.level_id;
    }
    return "";
  };

  const studentLevelId = getStudentLevelId();
  
  // Fetch announcements using React Query
  const { data: announcements = [], isLoading, error } = useQuery({
    queryKey: ["announcements", studentLevelId],
    queryFn: () => studentLevelId 
      ? getAnnouncementsByLevel(studentLevelId)
      : getAnnouncements(),
    enabled: !!student,
  });
  
  if (!student) {
    return (
      <div className="p-6 text-center">
        <h2 className="text-2xl font-bold text-red-600">Student Information Not Found</h2>
        <p className="text-gray-600 mt-2">Your student profile could not be loaded. Please contact support.</p>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-green-600" />
        <span className="ml-2 text-lg text-gray-600">Loading announcements...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 text-center">
        <AlertCircle className="h-12 w-12 text-red-600 mx-auto mb-2" />
        <h2 className="text-xl font-bold text-red-600">Error Loading Announcements</h2>
        <p className="text-gray-600 mt-2">{(error as Error).message || "An error occurred while loading announcements."}</p>
        <Button 
          className="mt-4"
          onClick={() => window.location.reload()}
        >
          Retry
        </Button>
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }).format(date);
    } catch (e) {
      return "Invalid date";
    }
  };

  const getTimeSince = (dateString: string) => {
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffInMs = now.getTime() - date.getTime();
      const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
      
      if (diffInDays === 0) {
        return "Today";
      } else if (diffInDays === 1) {
        return "Yesterday";
      } else if (diffInDays < 7) {
        return `${diffInDays} days ago`;
      } else if (diffInDays < 30) {
        const weeks = Math.floor(diffInDays / 7);
        return `${weeks} ${weeks === 1 ? 'week' : 'weeks'} ago`;
      } else {
        return formatDate(dateString);
      }
    } catch (e) {
      return "Unknown date";
    }
  };

  const getCategoryIcon = (category?: string) => {
    if (!category) return <Info className="h-5 w-5 text-gray-600" />;
    
    switch (category.toLowerCase()) {
      case "academic":
        return <BookOpen className="h-5 w-5 text-blue-600" />;
      case "administrative":
        return <Users className="h-5 w-5 text-purple-600" />;
      case "event":
        return <Calendar className="h-5 w-5 text-green-600" />;
      case "resource":
        return <BookOpen className="h-5 w-5 text-amber-600" />;
      case "policy":
        return <AlertCircle className="h-5 w-5 text-red-600" />;
      default:
        return <Info className="h-5 w-5 text-gray-600" />;
    }
  };

  const getPriorityBadge = (priority: string) => {
    if (!priority) return <Badge variant="outline">Normal Priority</Badge>;
    
    switch (priority.toLowerCase()) {
      case "high":
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">High Priority</Badge>;
      case "medium":
        return <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">Medium Priority</Badge>;
      case "low":
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Low Priority</Badge>;
      default:
        return <Badge variant="outline">Normal Priority</Badge>;
    }
  };

  const getCategory = (announcement: Announcement) => {
    return announcement.is_general ? "general" : "administrative";
  };

  const filteredAnnouncements = announcements.filter(announcement => {
    const matchesSearch = searchQuery === "" ||
      (announcement.title && announcement.title.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (announcement.content && announcement.content.toLowerCase().includes(searchQuery.toLowerCase()));

    const category = getCategory(announcement);
    const matchesFilter = filter === "all" || category === filter;

    return matchesSearch && matchesFilter;
  });

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Announcements & Notices</h1>
        <p className="text-muted-foreground">Stay updated with important school announcements and notices.</p>
      </div>

      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              placeholder="Search announcements..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
        <div>
          <Tabs defaultValue="all" className="w-full" onValueChange={setFilter}>
            <TabsList className="grid grid-cols-6">
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="academic">Academic</TabsTrigger>
              <TabsTrigger value="administrative">Admin</TabsTrigger>
              <TabsTrigger value="event">Events</TabsTrigger>
              <TabsTrigger value="general">General</TabsTrigger>
              <TabsTrigger value="policy">Policies</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      <div className="space-y-4">
        {filteredAnnouncements.length === 0 ? (
          <Card>
            <CardContent className="p-6 text-center">
              <p className="text-gray-500">No announcements found matching your search criteria.</p>
            </CardContent>
          </Card>
        ) : (
          filteredAnnouncements.map((announcement) => (
            <Card key={announcement.id} className="overflow-hidden">
              <CardContent className="p-6">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex items-center justify-center bg-gray-100 p-3 rounded-md md:self-start">
                    {getCategoryIcon(getCategory(announcement))}
                  </div>
                  <div className="flex-1">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                      <div>
                        <h3 className="text-lg font-semibold">{announcement.title}</h3>
                        <div className="flex items-center gap-2 mt-1">
                          {getPriorityBadge(announcement.priority)}
                          {announcement.is_general && (
                            <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                              <Globe className="h-3 w-3 mr-1" />
                              General
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div className="text-sm text-gray-500">
                        {getTimeSince(announcement.created_at)}
                      </div>
                    </div>
                    
                    <div className="mt-3 text-gray-700 whitespace-pre-line">
                      {announcement.content}
                    </div>
                    

                    
                    <div className="mt-4 pt-3 border-t border-gray-100 flex justify-between items-center">
                      <div className="text-sm text-gray-500">
                        Posted by: <span className="font-medium">School Admin</span>
                      </div>
                      <div className="text-sm text-gray-500">
                        Expires: <span className="font-medium">{announcement.expires_at ? formatDate(announcement.expires_at) : 'No expiry'}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Announcement Channels</CardTitle>
          <CardDescription>Other ways to stay updated with school announcements</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center p-3 border rounded-md">
              <div className="bg-blue-100 p-2 rounded-md mr-4">
                <Bell className="h-5 w-5 text-blue-600" />
              </div>
              <div className="flex-1">
                <h4 className="font-medium">Email Notifications</h4>
                <p className="text-sm text-gray-600">Receive announcements directly to your email</p>
              </div>
              <Button variant="outline" size="sm">
                Subscribe
              </Button>
            </div>
            <div className="flex items-center p-3 border rounded-md">
              <div className="bg-green-100 p-2 rounded-md mr-4">
                <Megaphone className="h-5 w-5 text-green-600" />
              </div>
              <div className="flex-1">
                <h4 className="font-medium">School Notice Board</h4>
                <p className="text-sm text-gray-600">Check the physical notice board in the main hall</p>
              </div>
              <Button variant="outline" size="sm" disabled>
                In-Person
              </Button>
            </div>
            <div className="flex items-center p-3 border rounded-md">
              <div className="bg-purple-100 p-2 rounded-md mr-4">
                <Bell className="h-5 w-5 text-purple-600" />
              </div>
              <div className="flex-1">
                <h4 className="font-medium">SMS Alerts</h4>
                <p className="text-sm text-gray-600">Get urgent announcements via SMS</p>
              </div>
              <Button variant="outline" size="sm">
                Enable
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Announcements; 