import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  FileText,
  Video,
  FileAudio,
  Image as ImageIcon,
  Download,
  Search,
  BookOpen,
  File,
  Clock,
  Loader2,
  AlertCircle
} from "lucide-react";
import { Student } from "@/types/student";
// Firebase storage removed - using PHP backend for file handling

interface CourseMaterialsProps {
  student: Student | null;
}

interface Material {
  id: string;
  title: string;
  description: string;
  subject: string;
  type: string;
  fileType: string;
  size: string;
  uploadDate: string;
  downloadCount: number;
  path: string;
  duration?: string;
}

interface Subject {
  id: string;
  name: string;
}

const CourseMaterials = ({ student }: CourseMaterialsProps) => {
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [selectedSubject, setSelectedSubject] = useState<string>("all");
  const [materials, setMaterials] = useState<Material[]>([]);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  

  
  useEffect(() => {
    const fetchMaterialsData = async () => {
      if (!student?.id) {
        setLoading(false);
        setError('Student information not available');
        return;
      }

      try {
        setLoading(true);

        // For now, use mock data since there's no course materials API endpoint yet
        // TODO: Replace with actual API call when course materials endpoint is available
        const mockMaterials: Material[] = [
          {
            id: '1',
            title: 'Introduction to Programming - Chapter 1',
            description: 'Basic programming concepts and syntax',
            subject: 'Computer Science',
            type: 'textbook',
            fileType: 'pdf',
            size: '2.5 MB',
            uploadDate: new Date().toISOString(),
            downloadCount: 45,
            path: '/materials/programming-ch1.pdf',
            duration: undefined
          },
          {
            id: '2',
            title: 'Data Structures Lecture Video',
            description: 'Introduction to arrays and linked lists',
            subject: 'Computer Science',
            type: 'video',
            fileType: 'mp4',
            size: '125 MB',
            uploadDate: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
            downloadCount: 32,
            path: '/materials/data-structures-intro.mp4',
            duration: '45 minutes'
          },
          {
            id: '3',
            title: 'Mathematics Formula Sheet',
            description: 'Essential formulas for calculus and algebra',
            subject: 'Mathematics',
            type: 'notes',
            fileType: 'pdf',
            size: '1.2 MB',
            uploadDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
            downloadCount: 67,
            path: '/materials/math-formulas.pdf',
            duration: undefined
          }
        ];

        console.log('Using mock course materials:', mockMaterials.length);
        setMaterials(mockMaterials);

        // Extract unique subjects
        const uniqueSubjects = [...new Set(mockMaterials.map(m => m.subject))];
        const subjectsData = uniqueSubjects.map(subject => ({ id: subject, name: subject }));

        setSubjects(subjectsData);
        setError(null);
      } catch (err) {
        console.error('Error fetching course materials:', err);
        setError('Failed to load course materials. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchMaterialsData();
  }, [student?.id]);

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (e) {
      return 'Invalid date';
    }
  };

  const getFileIcon = (fileType: string, type: string) => {
    if (type === "textbooks") return <BookOpen className="h-10 w-10 text-blue-600" />;
    if (type === "notes") return <FileText className="h-10 w-10 text-green-600" />;
    if (type === "videos") return <Video className="h-10 w-10 text-red-600" />;
    if (type === "audio") return <FileAudio className="h-10 w-10 text-purple-600" />;
    if (type === "worksheets") return <File className="h-10 w-10 text-amber-600" />;
    
    switch (fileType) {
      case "pdf":
        return <FileText className="h-10 w-10 text-red-600" />;
      case "mp4":
        return <Video className="h-10 w-10 text-blue-600" />;
      case "mp3":
        return <FileAudio className="h-10 w-10 text-green-600" />;
      case "jpg":
      case "png":
        return <ImageIcon className="h-10 w-10 text-purple-600" />;
      default:
        return <File className="h-10 w-10 text-gray-600" />;
    }
  };

  const getFileTypeBadge = (fileType: string) => {
    switch (fileType) {
      case "pdf":
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">PDF</Badge>;
      case "mp4":
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Video</Badge>;
      case "mp3":
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Audio</Badge>;
      case "jpg":
      case "png":
        return <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">Image</Badge>;
      default:
        return <Badge variant="outline">{fileType.toUpperCase()}</Badge>;
    }
  };
  
  const handleDownload = async (material: Material) => {
    try {
      // For now, simulate download since we're using mock data
      // TODO: Replace with actual file download from PHP backend
      console.log('Downloading material:', material.title);
      alert(`Download started for: ${material.title}`);

      // In a real implementation, this would make an API call to the PHP backend
      // to get the actual file URL and handle the download
    } catch (err) {
      console.error('Error downloading file:', err);
      alert('Failed to download file. Please try again later.');
    }
  };

  const filteredMaterials = materials.filter(material => {
    const matchesSearch = searchQuery === "" || 
      material.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      material.description.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesSubject = selectedSubject === "all" || material.subject === subjects.find(s => s.id === selectedSubject)?.name;
    
    return matchesSearch && matchesSubject;
  });

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="mt-4 text-muted-foreground">Loading course materials...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <AlertCircle className="h-8 w-8 text-red-500" />
        <p className="mt-4 text-red-500">{error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Course Materials</h1>
        <p className="text-muted-foreground">Access study resources, textbooks, and learning materials.</p>
      </div>

      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              placeholder="Search materials..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
        <div>
          <Tabs defaultValue="all" className="w-full" onValueChange={setSelectedSubject}>
            <TabsList className="grid grid-cols-4">
              <TabsTrigger value="all">All</TabsTrigger>
              {subjects.map((subject) => (
                <TabsTrigger key={subject.id} value={subject.id}>
                  {subject.name}
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredMaterials.length === 0 ? (
          <div className="col-span-full text-center py-12">
            <p className="text-muted-foreground">No materials found matching your criteria.</p>
          </div>
        ) : (
          filteredMaterials.map((material) => (
            <Card key={material.id} className="overflow-hidden">
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0">
                    {getFileIcon(material.fileType, material.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium truncate">{material.title}</h3>
                    <p className="text-sm text-gray-600 mt-1 line-clamp-2">{material.description}</p>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {getFileTypeBadge(material.fileType)}
                      <Badge variant="outline" className="bg-gray-100">{material.size}</Badge>
                      {material.duration && (
                        <Badge variant="outline" className="bg-gray-100">
                          <Clock className="h-3 w-3 mr-1" />
                          {material.duration}
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center justify-between mt-4">
                      <div className="text-xs text-gray-500">
                        Uploaded: {formatDate(material.uploadDate)}
                      </div>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleDownload(material)}
                      >
                        <Download className="h-4 w-4 mr-1" />
                        Download
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
};

export default CourseMaterials; 