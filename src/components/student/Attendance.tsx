import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import {
  Calendar,
  CheckCircle2,
  XCircle,
  AlertCircle,
  Loader2
} from "lucide-react";
import { Student } from "@/types/student";
import { getStudentAttendance, AttendanceRecord } from "@/api/attendance";
// Timestamp handling now done with regular Date objects

interface AttendanceProps {
  student: Student | null;
}

interface SubjectAttendance {
  name: string;
  present: number;
  absent: number;
  late: number;
  total: number;
  percentage: number;
}

interface AttendanceSummary {
  present: number;
  absent: number;
  late: number;
  total: number;
  percentage: number;
}

const Attendance = ({ student }: AttendanceProps) => {
  const [currentMonth, setCurrentMonth] = useState<string>(new Date().toLocaleString('default', { month: 'long' }));
  const [currentYear, setCurrentYear] = useState<string>(new Date().getFullYear().toString());
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);
  const [subjectAttendance, setSubjectAttendance] = useState<SubjectAttendance[]>([]);
  const [attendanceSummary, setAttendanceSummary] = useState<AttendanceSummary>({
    present: 0,
    absent: 0,
    late: 0,
    total: 0,
    percentage: 0
  });
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  // Get available months and years
  const months = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
  const currentDate = new Date();
  const years = [
    (currentDate.getFullYear() - 1).toString(),
    currentDate.getFullYear().toString(),
    (currentDate.getFullYear() + 1).toString()
  ];
  
  useEffect(() => {
    const fetchAttendanceData = async () => {
      if (!student?.id) {
        setLoading(false);
        setError('Student information not available');
        return;
      }

      try {
        setLoading(true);
        
        // Use the API function to get attendance records
        const attendanceData = await getStudentAttendance(student.id);
        
        if (!attendanceData || attendanceData.length === 0) {
          console.log('No attendance records found for student:', student.id);
          setError('No attendance records found for your account');
          setAttendanceRecords([]);
          setSubjectAttendance([]);
          setAttendanceSummary({
            present: 0,
            absent: 0,
            late: 0,
            total: 0,
            percentage: 0
          });
          setLoading(false);
          return;
        }
        
        console.log('Found attendance records:', attendanceData.length);
        setAttendanceRecords(attendanceData);
        
        // Calculate subject-wise attendance
        const subjectMap = new Map<string, { present: number; absent: number; late: number; total: number }>();
        
        attendanceData.forEach(record => {
          // Use a generic subject name since the API doesn't provide subject info
          const subject = 'General Attendance';

          if (!subjectMap.has(subject)) {
            subjectMap.set(subject, { present: 0, absent: 0, late: 0, total: 0 });
          }

          const subjectStats = subjectMap.get(subject)!;
          subjectStats.total++;

          switch (record.status) {
            case 'present':
              subjectStats.present++;
              break;
            case 'absent':
              subjectStats.absent++;
              break;
            case 'late':
              subjectStats.late++;
              break;
          }
        });
        
        const subjectAttendanceData = Array.from(subjectMap.entries()).map(([name, stats]) => ({
          name,
          ...stats,
          percentage: Math.round((stats.present / stats.total) * 100)
        }));
        
        setSubjectAttendance(subjectAttendanceData);
        
        // Calculate overall attendance summary
        const totalPresent = attendanceData.filter(record => record.status === 'present').length;
        const totalAbsent = attendanceData.filter(record => record.status === 'absent').length;
        const totalLate = attendanceData.filter(record => record.status === 'late').length;
        const totalClasses = totalPresent + totalAbsent + totalLate;
        
        setAttendanceSummary({
          present: totalPresent,
          absent: totalAbsent,
          late: totalLate,
          total: totalClasses,
          percentage: totalClasses > 0 ? Math.round((totalPresent / totalClasses) * 100) : 0
        });
        
        setError(null);
      } catch (err) {
        console.error('Error fetching attendance data:', err);
        setError('Failed to load attendance data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchAttendanceData();
  }, [student?.id]);
  
  const formatDate = (date: string) => {
    try {
      const dateObj = new Date(date);

      return dateObj.toLocaleDateString('en-US', {
        weekday: 'short',
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      });
    } catch (e) {
      console.error('Error formatting date:', e);
      return 'Invalid date';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "present":
        return <CheckCircle2 className="h-5 w-5 text-green-600" />;
      case "absent":
        return <XCircle className="h-5 w-5 text-red-600" />;
      case "late":
        return <AlertCircle className="h-5 w-5 text-amber-600" />;
      default:
        return null;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "present":
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Present</Badge>;
      case "absent":
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Absent</Badge>;
      case "late":
        return <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">Late</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getDateFromRecord = (record: AttendanceRecord): Date => {
    return new Date(record.date);
  };

  const filteredAttendance = attendanceRecords.filter(record => {
    const date = getDateFromRecord(record);
    return date.getMonth() === months.indexOf(currentMonth) && date.getFullYear().toString() === currentYear;
  });

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="mt-4 text-muted-foreground">Loading attendance data...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <AlertCircle className="h-8 w-8 text-red-500" />
        <p className="mt-4 text-red-500">{error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Attendance Records</h1>
        <p className="text-muted-foreground">View and track your attendance history.</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Overall Attendance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{attendanceSummary.percentage}%</div>
            <Progress value={attendanceSummary.percentage} className="h-2 mt-2" />
            <div className="grid grid-cols-3 gap-2 mt-4 text-center">
              <div>
                <div className="flex items-center justify-center">
                  <CheckCircle2 className="h-5 w-5 text-green-600" />
                </div>
                <div className="mt-1">
                  <div className="text-xl font-bold">{attendanceSummary.present}</div>
                  <div className="text-xs text-gray-500">Present</div>
                </div>
              </div>
              <div>
                <div className="flex items-center justify-center">
                  <XCircle className="h-5 w-5 text-red-600" />
                </div>
                <div className="mt-1">
                  <div className="text-xl font-bold">{attendanceSummary.absent}</div>
                  <div className="text-xs text-gray-500">Absent</div>
                </div>
              </div>
              <div>
                <div className="flex items-center justify-center">
                  <AlertCircle className="h-5 w-5 text-amber-600" />
                </div>
                <div className="mt-1">
                  <div className="text-xl font-bold">{attendanceSummary.late}</div>
                  <div className="text-xs text-gray-500">Late</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Subject-wise Attendance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {subjectAttendance.map((subject, index) => (
                <div key={index}>
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium">{subject.name}</span>
                    <span className="text-sm font-medium">{subject.percentage}%</span>
                  </div>
                  <div className="flex items-center">
                    <Progress value={subject.percentage} className="h-2 flex-1" />
                    <div className="ml-4 text-xs">
                      <span className="text-green-600">{subject.present}</span>
                      <span className="text-gray-400">/</span>
                      <span className="text-gray-600">{subject.total}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <CardTitle>Daily Attendance Log</CardTitle>
            <div className="flex items-center space-x-2">
              <Select value={currentMonth} onValueChange={setCurrentMonth}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="Month" />
                </SelectTrigger>
                <SelectContent>
                  {months.map((month) => (
                    <SelectItem key={month} value={month}>{month}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={currentYear} onValueChange={setCurrentYear}>
                <SelectTrigger className="w-[100px]">
                  <SelectValue placeholder="Year" />
                </SelectTrigger>
                <SelectContent>
                  {years.map((year) => (
                    <SelectItem key={year} value={year}>{year}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {filteredAttendance.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No attendance records found for this period.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredAttendance.map((record, index) => (
                <div key={index} className="flex items-start p-3 border rounded-md">
                  <div className="mr-4 mt-1">
                    {getStatusIcon(record.status)}
                  </div>
                  <div className="flex-1">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                      <div>
                        <h3 className="font-medium">General Attendance</h3>
                        <div className="flex items-center text-sm text-gray-600 mt-1">
                          <Calendar className="h-4 w-4 mr-1" />
                          {formatDate(record.date)}
                        </div>
                      </div>
                      <div className="mt-2 md:mt-0 flex items-center">
                        {getStatusBadge(record.status)}
                      </div>
                    </div>
                    {record.notes && (
                      <div className="mt-2 text-sm text-gray-600">
                        <p className="italic">{record.notes}</p>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default Attendance; 