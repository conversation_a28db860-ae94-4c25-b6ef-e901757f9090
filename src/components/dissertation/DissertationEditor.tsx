import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useEditor, EditorContent, NodeViewWrapper } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Image from '@tiptap/extension-image';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import TextAlign from '@tiptap/extension-text-align';
import FontFamily from '@tiptap/extension-font-family';
import TextStyle from '@tiptap/extension-text-style';
import Color from '@tiptap/extension-color';
import Underline from '@tiptap/extension-underline';
import BulletList from '@tiptap/extension-bullet-list';
import OrderedList from '@tiptap/extension-ordered-list';
import ListItem from '@tiptap/extension-list-item';
import Highlight from '@tiptap/extension-highlight';
import { NodeViewRenderer } from '@tiptap/core';
import { ResizableImage } from './ResizableImage';
import { DraggableTable } from './DraggableTable';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Save,
  Settings,
  MessageSquare,
  History,
  Users,
  FileText,
  Clock,
  CheckCircle,
  AlertCircle,
  ArrowLeft,
  Download,
  Share,
  Eye,
  EyeOff,
  Plus,
  Minus,
  RotateCcw,
  PlusCircle
} from 'lucide-react';

import { DissertationToolbar } from './DissertationToolbar';
import { CommentPanel } from './CommentPanel';
import { ReviewPanel } from './ReviewPanel';
import { WordCountDisplay } from './WordCountDisplay';
import { AutoSaveIndicator } from './AutoSaveIndicator';
import { PageNavigation } from './PageNavigation';
import { CollaborationPanel } from './CollaborationPanel';
import { VersionHistory } from './VersionHistory';
import { DocumentSettings } from './DocumentSettings';

import { useDissertations } from '@/hooks/useDissertations';
import { useAuth } from '@/contexts/AuthContext';
import { 
  DissertationDocument, 
  DissertationComment, 
  EditorState, 
  ReviewState,
  CollaborationCursor
} from '@/types/dissertation';
import { toast } from 'sonner';
// Array operations now handled by PHP backend

interface DissertationEditorProps {
  // No props required for this component
}

const DissertationEditor: React.FC<DissertationEditorProps> = () => {
  const { documentId } = useParams<{ documentId: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  
  const {
    currentDissertation,
    loading,
    error,
    loadDissertation,
    updateDissertation,
    saveDissertationContent,
    loadComments,
    addComment,
    updateComment,
    deleteComment,
    comments,
    loadVersions,
    createVersion,
    versions,
    loadSettings,
    saveSettings,
    settings,
    subscribeToDocument,
    logActivity
  } = useDissertations();

  // Editor state
  const [editorState, setEditorState] = useState<EditorState>({
    isEditing: false,
    currentPage: 1,
    totalPages: 1,
    wordCount: 0,
    characterCount: 0,
    selectedText: '',
    cursorPosition: 0,
    isAutoSaving: false,
    lastSaved: new Date(),
    hasUnsavedChanges: false
  });

  // Review state
  const [reviewState, setReviewState] = useState<ReviewState>({
    isReviewMode: false,
    activeComments: [],
    showPrivateComments: false
  });

  // UI state
  const [activeTab, setActiveTab] = useState('editor');
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [showComments, setShowComments] = useState(true);
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true);
  const [collaborators, setCollaborators] = useState<CollaborationCursor[]>([]);
  
  // Refs
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout>();
  const unsubscribeRef = useRef<(() => void) | null>(null);

  // TipTap Editor configuration
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3, 4, 5, 6],
        },
        bulletList: false,
        orderedList: false,
        listItem: false,
      }),
      Image.configure({
        inline: false,
        allowBase64: true,
        HTMLAttributes: {
          class: 'dissertation-image',
          draggable: false,
        },
        addAttributes() {
          return {
            ...this.parent?.(),
            width: {
              default: null,
              renderHTML: attributes => {
                if (!attributes.width) {
                  return {};
                }
                return {
                  width: attributes.width,
                };
              },
            },
            height: {
              default: null,
              renderHTML: attributes => {
                if (!attributes.height) {
                  return {};
                }
                return {
                  height: attributes.height,
                };
              },
            },
            x: {
              default: 0,
              renderHTML: attributes => {
                return {};
              },
            },
            y: {
              default: 0,
              renderHTML: attributes => {
                return {};
              },
            },
          };
        },
        nodeView: () => {
          return ({ node, editor, getPos, updateAttributes, deleteNode }) => {
            return new ResizableImage({
              node,
              editor,
              getPos,
              updateAttributes,
              deleteNode,
            });
          };
        },
      }),
      Table.configure({
        resizable: true,
        allowTableNodeSelection: true,
        HTMLAttributes: {
          class: 'dissertation-table',
        },
        addAttributes() {
          return {
            ...this.parent?.(),
            width: {
              default: '100%',
              renderHTML: attributes => {
                if (!attributes.width) {
                  return {};
                }
                return {
                  width: attributes.width,
                };
              },
            },
            height: {
              default: null,
              renderHTML: attributes => {
                if (!attributes.height) {
                  return {};
                }
                return {
                  height: attributes.height,
                };
              },
            },
            x: {
              default: 0,
              renderHTML: attributes => {
                return {};
              },
            },
            y: {
              default: 0,
              renderHTML: attributes => {
                return {};
              },
            },
          };
        },
        nodeView: () => {
          return ({ node, editor, getPos, updateAttributes, deleteNode }) => {
            return new DraggableTable({
              node,
              editor,
              getPos,
              updateAttributes,
              deleteNode,
            });
          };
        },
      }),
      TableRow.configure({
        HTMLAttributes: {
          class: 'dissertation-table-row',
        },
      }),
      TableHeader.configure({
        HTMLAttributes: {
          class: 'dissertation-table-header',
          style: 'background-color: #f8f9fa; font-weight: bold;'
        },
      }),
      TableCell.configure({
        HTMLAttributes: {
          class: 'dissertation-table-cell',
          style: 'border: 1px solid #000; padding: 6pt 8pt;'
        },
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph', 'tableCell', 'tableHeader'],
        alignments: ['left', 'center', 'right', 'justify'],
        defaultAlignment: 'left',
      }),
      FontFamily.configure({
        types: ['textStyle'],
      }),
      TextStyle,
      Color.configure({
        types: ['textStyle'],
      }),
      Underline,
      BulletList.configure({
        HTMLAttributes: {
          class: 'dissertation-bullet-list',
        },
      }),
      OrderedList.configure({
        HTMLAttributes: {
          class: 'dissertation-ordered-list',
        },
      }),
      ListItem.configure({
        HTMLAttributes: {
          class: 'dissertation-list-item',
        },
      }),
      Highlight.configure({
        HTMLAttributes: {
          class: 'dissertation-highlight',
        },
      }),
    ],
    content: currentDissertation?.content || '<p>Loading...</p>',
    editorProps: {
      attributes: {
        class: 'dissertation-content prose prose-lg max-w-none focus:outline-none min-h-[800px] px-16 py-12',
        style: 'background: white; min-height: 297mm; width: 210mm; margin: 0 auto; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); font-family: "Times New Roman", serif; line-height: 1.8; font-size: 12pt;'
      },
    },
    onUpdate: ({ editor }) => {
      handleContentChange(editor.getHTML());
    },
    onSelectionUpdate: ({ editor }) => {
      const { from, to } = editor.state.selection;
      const selectedText = editor.state.doc.textBetween(from, to);
      
      setEditorState(prev => ({
        ...prev,
        cursorPosition: from,
        selectedText
      }));
    },
  }, [currentDissertation?.content]);

  // Load dissertation data
  useEffect(() => {
    if (documentId && user?.uid) {
      loadDissertation(documentId);
      loadComments(documentId);
      loadVersions(documentId);
      loadSettings(documentId);
      
      // Set up real-time subscription
      const unsubscribe = subscribeToDocument(documentId);
      unsubscribeRef.current = unsubscribe;
      
      // Register user as active collaborator
      if (user?.uid && documentId) {
        updateDissertation(documentId, {
          active_collaborators: arrayUnion(user.uid)
        });
        
        // Set up a collaborator cursor for real-time editing
        const cursorColor = getRandomColor();
        setCollaborators(prev => [
          ...prev,
          {
            user_id: user.uid,
            user_name: user.displayName || 'Anonymous',
            user_role: user.role === 'teacher' ? 'supervisor' : 'student',
            position: 0,
            color: cursorColor,
            last_updated: new Date()
          }
        ]);
        
        // Remove user from active collaborators when leaving
        const handleBeforeUnload = () => {
          if (user?.uid && documentId) {
            updateDissertation(documentId, {
              active_collaborators: arrayRemove(user.uid)
            });
          }
        };
        
        window.addEventListener('beforeunload', handleBeforeUnload);
        
        return () => {
          window.removeEventListener('beforeunload', handleBeforeUnload);
          if (unsubscribeRef.current) {
            unsubscribeRef.current();
          }
          if (user?.uid && documentId) {
            updateDissertation(documentId, {
              active_collaborators: arrayRemove(user.uid)
            });
          }
        };
      }
      
      return () => {
        if (unsubscribeRef.current) {
          unsubscribeRef.current();
        }
      };
    }
  }, [documentId, user?.uid]);

  // Update editor content when dissertation loads
  useEffect(() => {
    if (currentDissertation && editor && !editor.isDestroyed) {
      editor.commands.setContent(currentDissertation.content);
      updateWordCount(currentDissertation.content);
    }
  }, [currentDissertation, editor]);

  // Auto-save functionality
  const handleContentChange = useCallback((content: string) => {
    if (!currentDissertation?.id || !autoSaveEnabled) return;
    
    setEditorState(prev => ({ ...prev, hasUnsavedChanges: true }));
    
    // Clear existing timeout
    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current);
    }
    
    // Set new timeout for auto-save
    autoSaveTimeoutRef.current = setTimeout(async () => {
      await handleSave(content);
    }, 2000); // Auto-save after 2 seconds of no changes
  }, [currentDissertation?.id, autoSaveEnabled]);

  // Manual save function
  const handleSave = async (content?: string) => {
    if (!currentDissertation?.id || !editor) return;
    
    const contentToSave = content || editor.getHTML();
    
    setEditorState(prev => ({ ...prev, isAutoSaving: true }));
    
    try {
      await saveDissertationContent(currentDissertation.id, contentToSave);
      
      setEditorState(prev => ({
        ...prev,
        isAutoSaving: false,
        hasUnsavedChanges: false,
        lastSaved: new Date()
      }));
      
      // Log activity
      await logActivity({
        student_id: user?.uid || '',
        document_id: currentDissertation.id,
        activity_type: 'save',
        details: 'Document saved'
      });
      
    } catch (error) {
      console.error('Save failed:', error);
      setEditorState(prev => ({ ...prev, isAutoSaving: false }));
      toast.error('Failed to save document');
    }
  };

  // Update word count
  const updateWordCount = useCallback((content: string) => {
    const text = content.replace(/<[^>]*>/g, '').trim();
    const words = text.split(/\s+/).filter(word => word.length > 0);
    const characters = text.length;
    const pages = Math.max(1, Math.ceil(words.length / 250));
    
    setEditorState(prev => ({
      ...prev,
      wordCount: words.length,
      characterCount: characters,
      totalPages: pages
    }));
  }, []);

  // Comment handlers
  const handleAddComment = async (comment: Omit<DissertationComment, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      await addComment({
        ...comment,
        author_id: user?.uid || '',
        author_name: user?.displayName || 'Anonymous',
        author_role: user?.role === 'teacher' ? 'supervisor' : 'student'
      });
    } catch (error) {
      console.error('Failed to add comment:', error);
    }
  };

  // Version handlers
  const handleCreateVersion = async () => {
    if (!currentDissertation?.id || !editor) return;
    
    const content = editor.getHTML();
    const changeSummary = `Version created on ${new Date().toLocaleDateString()}`;
    
    try {
      await createVersion(currentDissertation.id, content, changeSummary);
      toast.success('New version created successfully');
    } catch (error) {
      console.error('Failed to create version:', error);
    }
  };

  // Review mode toggle
  const toggleReviewMode = () => {
    setReviewState(prev => ({
      ...prev,
      isReviewMode: !prev.isReviewMode
    }));
  };

  // Page break insertion
  const insertPageBreak = () => {
    if (editor) {
      editor.chain().focus().insertContent('<div class="page-break"></div>').run();
    }
  };

  // Status update
  const updateStatus = async (newStatus: DissertationDocument['status']) => {
    if (!currentDissertation?.id) return;
    
    try {
      await updateDissertation(currentDissertation.id, { status: newStatus });
      toast.success(`Status updated to ${newStatus.replace('_', ' ')}`);
    } catch (error) {
      console.error('Failed to update status:', error);
    }
  };

  // Helper function to generate random colors for collaborators
  const getRandomColor = () => {
    const colors = [
      '#f44336', '#e91e63', '#9c27b0', '#673ab7', '#3f51b5',
      '#2196f3', '#03a9f4', '#00bcd4', '#009688', '#4caf50',
      '#8bc34a', '#cddc39', '#ffc107', '#ff9800', '#ff5722',
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  };

  if (loading && !currentDissertation) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-screen space-y-4">
        <AlertCircle className="w-12 h-12 text-red-500" />
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900">Error Loading Dissertation</h3>
          <p className="text-gray-600 mt-1">{error}</p>
          <Button onClick={() => navigate('/student')} className="mt-4">
            Back to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  if (!currentDissertation) {
    return (
      <div className="flex flex-col items-center justify-center h-screen space-y-4">
        <FileText className="w-12 h-12 text-gray-400" />
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900">Dissertation Not Found</h3>
          <p className="text-gray-600 mt-1">The requested dissertation could not be found.</p>
          <Button onClick={() => navigate('/student')} className="mt-4">
            Back to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/student')}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Dashboard
            </Button>
            <Separator orientation="vertical" className="h-6" />
            <div>
              <h1 className="text-xl font-semibold text-gray-900 line-clamp-1">
                {currentDissertation.title}
              </h1>
              <div className="flex items-center space-x-4 mt-1">
                <Badge className={`
                  text-xs
                  ${currentDissertation.status === 'draft' ? 'bg-gray-100 text-gray-800' : ''}
                  ${currentDissertation.status === 'ready_for_review' ? 'bg-blue-100 text-blue-800' : ''}
                  ${currentDissertation.status === 'under_review' ? 'bg-yellow-100 text-yellow-800' : ''}
                  ${currentDissertation.status === 'needs_revision' ? 'bg-red-100 text-red-800' : ''}
                  ${currentDissertation.status === 'approved' ? 'bg-green-100 text-green-800' : ''}
                `}>
                  {currentDissertation.status.replace('_', ' ').toUpperCase()}
                </Badge>
                <WordCountDisplay 
                  wordCount={editorState.wordCount}
                  characterCount={editorState.characterCount}
                  target={settings?.target_word_count}
                  showProgress={true}
                />
                <AutoSaveIndicator 
                  isAutoSaving={editorState.isAutoSaving}
                  lastSaved={editorState.lastSaved}
                  hasUnsavedChanges={editorState.hasUnsavedChanges}
                />
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowComments(!showComments)}
            >
              {showComments ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              {showComments ? 'Hide' : 'Show'} Comments
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={toggleReviewMode}
            >
              {reviewState.isReviewMode ? 'Exit Review' : 'Review Mode'}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleSave()}
              disabled={editorState.isAutoSaving}
            >
              <Save className="w-4 h-4 mr-2" />
              Save
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Editor Area */}
        <div className={`flex-1 flex flex-col ${isCollapsed ? 'mr-0' : 'mr-80'} transition-all duration-300`}>
          {/* Toolbar */}
          <DissertationToolbar
            editor={editor}
            onInsertPageBreak={insertPageBreak}
            onToggleReviewMode={toggleReviewMode}
            isReviewMode={reviewState.isReviewMode}
          />
          
          {/* Editor Content */}
          <div className="flex-1 overflow-auto bg-gray-100 p-8">
            <div className="dissertation-editor-container">
              <div className="bg-white shadow-lg relative overflow-visible">
                <EditorContent editor={editor} className="overflow-visible" />
              </div>
            </div>
          </div>
          
          {/* Page Navigation */}
          <PageNavigation
            currentPage={editorState.currentPage}
            totalPages={editorState.totalPages}
            onPageChange={(page) => setEditorState(prev => ({ ...prev, currentPage: page }))}
          />
        </div>

        {/* Right Sidebar */}
        {!isCollapsed && (
          <div className="w-80 border-l border-gray-200 bg-white flex flex-col">
            <div className="p-4 border-b">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold">Document Tools</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsCollapsed(true)}
                >
                  <Minus className="w-4 h-4" />
                </Button>
              </div>
            </div>
            
            <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
              <TabsList className="grid w-full grid-cols-4 mx-4 mt-2">
                <TabsTrigger value="comments">
                  <MessageSquare className="w-4 h-4" />
                </TabsTrigger>
                <TabsTrigger value="versions">
                  <History className="w-4 h-4" />
                </TabsTrigger>
                <TabsTrigger value="collaboration">
                  <Users className="w-4 h-4" />
                </TabsTrigger>
                <TabsTrigger value="settings">
                  <Settings className="w-4 h-4" />
                </TabsTrigger>
              </TabsList>
              
              <div className="flex-1 overflow-hidden">
                <TabsContent value="comments" className="h-full m-0">
                  <CommentPanel
                    comments={comments}
                    onAddComment={handleAddComment}
                    onUpdateComment={updateComment}
                    onDeleteComment={deleteComment}
                    currentUser={user}
                    documentId={currentDissertation.id}
                    isVisible={showComments}
                  />
                </TabsContent>
                
                <TabsContent value="versions" className="h-full m-0">
                  <VersionHistory
                    versions={versions}
                    currentVersion={currentDissertation.version}
                    onCreateVersion={handleCreateVersion}
                    onRestoreVersion={(version) => {
                      if (editor) {
                        editor.commands.setContent(version.content);
                        toast.success(`Restored to version ${version.version_number}`);
                      }
                    }}
                  />
                </TabsContent>
                
                <TabsContent value="collaboration" className="h-full m-0">
                  <CollaborationPanel
                    collaborators={collaborators}
                    documentId={currentDissertation.id}
                    currentUser={user}
                  />
                </TabsContent>
                
                <TabsContent value="settings" className="h-full m-0">
                  <DocumentSettings
                    settings={settings}
                    onSaveSettings={saveSettings}
                    documentId={currentDissertation.id}
                  />
                </TabsContent>
              </div>
            </Tabs>
          </div>
        )}
        
        {/* Collapsed Sidebar Toggle */}
        {isCollapsed && (
          <div className="w-12 border-l border-gray-200 bg-white flex flex-col items-center py-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsCollapsed(false)}
              className="mb-4"
            >
              <Plus className="w-4 h-4" />
            </Button>
            <div className="space-y-2">
              <Button
                variant={activeTab === 'comments' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => {
                  setActiveTab('comments');
                  setIsCollapsed(false);
                }}
              >
                <MessageSquare className="w-4 h-4" />
              </Button>
              <Button
                variant={activeTab === 'versions' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => {
                  setActiveTab('versions');
                  setIsCollapsed(false);
                }}
              >
                <History className="w-4 h-4" />
              </Button>
              <Button
                variant={activeTab === 'collaboration' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => {
                  setActiveTab('collaboration');
                  setIsCollapsed(false);
                }}
              >
                <Users className="w-4 h-4" />
              </Button>
              <Button
                variant={activeTab === 'settings' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => {
                  setActiveTab('settings');
                  setIsCollapsed(false);
                }}
              >
                <Settings className="w-4 h-4" />
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DissertationEditor;