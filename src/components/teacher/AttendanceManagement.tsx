import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { Loader2, Search, Calendar, CheckCircle, XCircle, Download, Filter } from 'lucide-react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getLevels } from '@/api/levels';
import { getStudentsByLevel } from '@/api/students';
import { getAttendanceByDate, getAttendanceByDateRange, markAttendance } from '@/api/attendance';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { format } from 'date-fns';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";

const AttendanceManagement = () => {
  const { userProfile } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [selectedLevel, setSelectedLevel] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [attendanceData, setAttendanceData] = useState<Record<string, boolean>>({});
  const [activeTab, setActiveTab] = useState<'mark' | 'report'>('mark');
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: new Date(new Date().setDate(new Date().getDate() - 7)),
    to: new Date(),
  });

  // Fetch levels data
  const { data: levels = [], isLoading: isLoadingLevels } = useQuery({
    queryKey: ['levels'],
    queryFn: getLevels
  });

  // Filter levels to only show those assigned to the teacher
  const assignedLevels = levels.filter(level => 
    userProfile?.assigned_levels?.includes(level.id)
  );

  // Fetch students for the selected level
  const { 
    data: students = [], 
    isLoading: isLoadingStudents,
  } = useQuery({
    queryKey: ['students', selectedLevel],
    queryFn: () => selectedLevel ? getStudentsByLevel(selectedLevel) : Promise.resolve([]),
    enabled: !!selectedLevel,
  });

  // Fetch attendance data for the selected date and level
  const {
    data: existingAttendance = [],
    isLoading: isLoadingAttendance,
    refetch: refetchAttendance
  } = useQuery({
    queryKey: ['attendance', selectedLevel, format(selectedDate, 'yyyy-MM-dd')],
    queryFn: () => selectedLevel
      ? getAttendanceByDate(selectedLevel, format(selectedDate, 'yyyy-MM-dd'))
      : Promise.resolve([]),
    enabled: !!selectedLevel,
  });

  // Initialize attendance data when existingAttendance changes
  useEffect(() => {
    if (existingAttendance.length > 0) {
      const newAttendanceData: Record<string, boolean> = {};
      existingAttendance.forEach(record => {
        newAttendanceData[record.student_id] = record.present;
      });
      setAttendanceData(newAttendanceData);
    }
  }, [existingAttendance]);

  // Fetch attendance report data
  const {
    data: attendanceReport = [],
    isLoading: isLoadingReport,
  } = useQuery({
    queryKey: [
      'attendance-report', 
      selectedLevel, 
      dateRange.from ? format(dateRange.from, 'yyyy-MM-dd') : null,
      dateRange.to ? format(dateRange.to, 'yyyy-MM-dd') : null
    ],
    queryFn: () => {
      if (!selectedLevel || !dateRange.from || !dateRange.to) return Promise.resolve([]);
      
      return getAttendanceByDateRange(
        selectedLevel, 
        format(dateRange.from, 'yyyy-MM-dd'),
        format(dateRange.to, 'yyyy-MM-dd')
      );
    },
    enabled: !!selectedLevel && !!dateRange.from && !!dateRange.to && activeTab === 'report',
  });

  // Mark attendance mutation
  const markAttendanceMutation = useMutation({
    mutationFn: (data: {
      level_id: string;
      date: string;
      attendance: { student_id: string; present: boolean }[];
    }) => markAttendance(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ 
        queryKey: ['attendance', selectedLevel, format(selectedDate, 'yyyy-MM-dd')] 
      });
      toast({
        title: "Success",
        description: "Attendance has been saved successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to save attendance",
        variant: "destructive",
      });
    }
  });

  // Filter students based on search term
  const filteredStudents = students.filter(student => 
    student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.student_id?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleLevelSelect = (levelId: string) => {
    setSelectedLevel(levelId);
    setSearchTerm('');
    setAttendanceData({});
  };

  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      setSelectedDate(date);
    }
  };

  const toggleAttendance = (studentId: string) => {
    setAttendanceData(prev => ({
      ...prev,
      [studentId]: !prev[studentId]
    }));
  };

  const handleMarkAllPresent = () => {
    const newAttendanceData: Record<string, boolean> = {};
    students.forEach(student => {
      newAttendanceData[student.id] = true;
    });
    setAttendanceData(newAttendanceData);
  };

  const handleMarkAllAbsent = () => {
    const newAttendanceData: Record<string, boolean> = {};
    students.forEach(student => {
      newAttendanceData[student.id] = false;
    });
    setAttendanceData(newAttendanceData);
  };

  const handleSaveAttendance = async () => {
    if (!selectedLevel) return;
    
    const attendanceRecords = Object.entries(attendanceData).map(([studentId, present]) => ({
      student_id: studentId,
      present
    }));
    
    await markAttendanceMutation.mutateAsync({
      level_id: selectedLevel,
      date: format(selectedDate, 'yyyy-MM-dd'),
      attendance: attendanceRecords
    });
  };

  const handleExportAttendance = () => {
    if (!selectedLevel || !attendanceReport.length) {
      toast({
        title: "No data to export",
        description: "Please select a level and date range with attendance data first.",
        variant: "destructive",
      });
      return;
    }

    // Get the level name
    const levelName = levels.find(l => l.id === selectedLevel)?.name || 'Unknown Level';
    
    // Create CSV content
    const headers = ['Date', 'Student ID', 'Student Name', 'Status'];
    const csvContent = [
      headers.join(','),
      ...attendanceReport.map(record => [
        `"${record.date}"`,
        `"${record.student_id}"`,
        `"${record.student_name || ''}"`,
        `"${record.present ? 'Present' : 'Absent'}"`,
      ].join(','))
    ].join('\n');
    
    // Create and download the file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `${levelName}_Attendance_${format(dateRange.from || new Date(), 'yyyy-MM-dd')}_to_${format(dateRange.to || new Date(), 'yyyy-MM-dd')}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast({
      title: "Export Successful",
      description: `Attendance report for ${levelName} has been exported.`,
    });
  };

  if (!userProfile) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
      </div>
    );
  }

  if (isLoadingLevels) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading levels...</span>
      </div>
    );
  }

  if (assignedLevels.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 space-y-4">
        <div className="rounded-full bg-yellow-100 p-3">
          <Calendar className="h-6 w-6 text-yellow-600" />
        </div>
        <h3 className="text-lg font-medium">No Levels Assigned</h3>
        <p className="text-sm text-muted-foreground text-center max-w-md">
          You don't have any levels assigned to you yet. Please contact an administrator to assign levels to your profile.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-2xl font-bold">Attendance Management</h3>
        <div className="flex items-center space-x-2">
          {activeTab === 'report' && (
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleExportAttendance}
              disabled={!selectedLevel || !attendanceReport.length}
            >
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
          )}
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'mark' | 'report')}>
        <TabsList>
          <TabsTrigger value="mark">Mark Attendance</TabsTrigger>
          <TabsTrigger value="report">Attendance Reports</TabsTrigger>
        </TabsList>
        
        <TabsContent value="mark" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {/* Levels Sidebar */}
            <Card className="md:col-span-1">
              <CardHeader>
                <CardTitle>Your Levels</CardTitle>
                <CardDescription>
                  Select a level to mark attendance
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {assignedLevels.map(level => (
                    <Button
                      key={level.id}
                      variant={selectedLevel === level.id ? "default" : "outline"}
                      className="w-full justify-start"
                      onClick={() => handleLevelSelect(level.id)}
                    >
                      {level.name}
                      <Badge variant="secondary" className="ml-auto">
                        {level.students?.length || 0}
                      </Badge>
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Attendance Marking */}
            <Card className="md:col-span-3">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>
                      {selectedLevel 
                        ? `Mark Attendance: ${levels.find(l => l.id === selectedLevel)?.name || 'Selected Level'}`
                        : 'Select a Level'
                      }
                    </CardTitle>
                    <CardDescription>
                      {selectedLevel 
                        ? `${filteredStudents.length} students enrolled`
                        : 'Choose a level from the sidebar to mark attendance'
                      }
                    </CardDescription>
                  </div>
                  {selectedLevel && (
                    <div className="flex items-center space-x-2">
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button variant="outline" size="sm">
                            <Calendar className="h-4 w-4 mr-2" />
                            {format(selectedDate, 'PPP')}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <CalendarComponent
                            mode="single"
                            selected={selectedDate}
                            onSelect={handleDateSelect}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <div className="relative w-64">
                        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="Search students..."
                          className="pl-8"
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                        />
                      </div>
                    </div>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                {!selectedLevel ? (
                  <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
                    <Calendar className="h-12 w-12 mb-4" />
                    <p>Select a level to mark attendance</p>
                  </div>
                ) : isLoadingStudents || isLoadingAttendance ? (
                  <div className="flex items-center justify-center h-64">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    <span className="ml-2">Loading students...</span>
                  </div>
                ) : filteredStudents.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
                    {searchTerm ? (
                      <>
                        <Search className="h-12 w-12 mb-4" />
                        <p>No students found matching "{searchTerm}"</p>
                      </>
                    ) : (
                      <>
                        <Calendar className="h-12 w-12 mb-4" />
                        <p>No students enrolled in this level</p>
                      </>
                    )}
                  </div>
                ) : (
                  <>
                    <div className="flex justify-between mb-4">
                      <div className="space-x-2">
                        <Button size="sm" variant="outline" onClick={handleMarkAllPresent}>
                          <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                          Mark All Present
                        </Button>
                        <Button size="sm" variant="outline" onClick={handleMarkAllAbsent}>
                          <XCircle className="h-4 w-4 mr-2 text-red-500" />
                          Mark All Absent
                        </Button>
                      </div>
                      <Button onClick={handleSaveAttendance} disabled={markAttendanceMutation.isPending}>
                        {markAttendanceMutation.isPending ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Saving...
                          </>
                        ) : (
                          'Save Attendance'
                        )}
                      </Button>
                    </div>
                    <div className="rounded-md border">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Name</TableHead>
                            <TableHead>Student ID</TableHead>
                            <TableHead className="text-right">Attendance</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {filteredStudents.map(student => (
                            <TableRow key={student.id}>
                              <TableCell className="font-medium">{student.name}</TableCell>
                              <TableCell>{student.student_id || '-'}</TableCell>
                              <TableCell className="text-right">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => toggleAttendance(student.id)}
                                  className={
                                    attendanceData[student.id] === undefined
                                      ? 'text-gray-400'
                                      : attendanceData[student.id]
                                      ? 'text-green-500 hover:text-green-600'
                                      : 'text-red-500 hover:text-red-600'
                                  }
                                >
                                  {attendanceData[student.id] === undefined ? (
                                    'Not Marked'
                                  ) : attendanceData[student.id] ? (
                                    <div className="flex items-center">
                                      <CheckCircle className="h-5 w-5 mr-1" />
                                      Present
                                    </div>
                                  ) : (
                                    <div className="flex items-center">
                                      <XCircle className="h-5 w-5 mr-1" />
                                      Absent
                                    </div>
                                  )}
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="report" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {/* Levels Sidebar */}
            <Card className="md:col-span-1">
              <CardHeader>
                <CardTitle>Your Levels</CardTitle>
                <CardDescription>
                  Select a level to view reports
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {assignedLevels.map(level => (
                    <Button
                      key={level.id}
                      variant={selectedLevel === level.id ? "default" : "outline"}
                      className="w-full justify-start"
                      onClick={() => handleLevelSelect(level.id)}
                    >
                      {level.name}
                      <Badge variant="secondary" className="ml-auto">
                        {level.students?.length || 0}
                      </Badge>
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Attendance Reports */}
            <Card className="md:col-span-3">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>
                      {selectedLevel 
                        ? `Attendance Report: ${levels.find(l => l.id === selectedLevel)?.name || 'Selected Level'}`
                        : 'Select a Level'
                      }
                    </CardTitle>
                    <CardDescription>
                      {selectedLevel 
                        ? `View attendance records for the selected date range`
                        : 'Choose a level from the sidebar to view attendance reports'
                      }
                    </CardDescription>
                  </div>
                  {selectedLevel && (
                    <div className="flex items-center space-x-2">
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button variant="outline" size="sm">
                            <Calendar className="h-4 w-4 mr-2" />
                            {dateRange.from ? format(dateRange.from, 'PPP') : 'From'} - {dateRange.to ? format(dateRange.to, 'PPP') : 'To'}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <CalendarComponent
                            mode="range"
                            selected={dateRange}
                            onSelect={setDateRange}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <div className="relative w-64">
                        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="Search students..."
                          className="pl-8"
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                        />
                      </div>
                    </div>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                {!selectedLevel ? (
                  <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
                    <Calendar className="h-12 w-12 mb-4" />
                    <p>Select a level to view attendance reports</p>
                  </div>
                ) : isLoadingReport ? (
                  <div className="flex items-center justify-center h-64">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    <span className="ml-2">Loading attendance data...</span>
                  </div>
                ) : attendanceReport.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
                    <Calendar className="h-12 w-12 mb-4" />
                    <p>No attendance records found for the selected date range</p>
                  </div>
                ) : (
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Date</TableHead>
                          <TableHead>Student</TableHead>
                          <TableHead>Student ID</TableHead>
                          <TableHead className="text-right">Status</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {attendanceReport
                          .filter(record => 
                            record.student_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            record.student_id?.toLowerCase().includes(searchTerm.toLowerCase())
                          )
                          .map((record, index) => (
                            <TableRow key={index}>
                              <TableCell>{format(new Date(record.date), 'PPP')}</TableCell>
                              <TableCell className="font-medium">{record.student_name}</TableCell>
                              <TableCell>{record.student_id}</TableCell>
                              <TableCell className="text-right">
                                <Badge variant={record.present ? "default" : "destructive"}>
                                  {record.present ? 'Present' : 'Absent'}
                                </Badge>
                              </TableCell>
                            </TableRow>
                          ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AttendanceManagement; 