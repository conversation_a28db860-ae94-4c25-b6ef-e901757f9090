import { useState, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { Loader2, Plus, FileText, Folder, Download, Trash2, Upload, File, Video, FileImage } from 'lucide-react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getLevels } from '@/api/levels';
import { getMaterials, createMaterial, deleteMaterial, MaterialFormData } from '@/api/materials';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";

// Extended user profile interface for teachers
interface TeacherProfile {
  assigned_levels?: string[];
  assigned_courses?: string[];
}

// Material type definitions
const MATERIAL_TYPES = [
  { value: 'document', label: 'Document', icon: FileText },
  { value: 'worksheet', label: 'Worksheet', icon: File },
  { value: 'presentation', label: 'Presentation', icon: FileText },
  { value: 'video', label: 'Video', icon: Video },
  { value: 'audio', label: 'Audio', icon: FileImage },
];

const CourseMaterials = () => {
  const { userProfile } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('all');
  const [selectedLevel, setSelectedLevel] = useState<string>('');
  const [isUploading, setIsUploading] = useState(false);

  // Cast userProfile to include teacher-specific properties
  const teacherProfile = userProfile as (typeof userProfile & TeacherProfile) | null;

  // Form state
  const [formData, setFormData] = useState<MaterialFormData>({
    title: '',
    description: '',
    level_id: '',
    type: 'document',
    file: undefined
  });

  // Fetch levels data
  const { data: levels = [], isLoading: isLoadingLevels } = useQuery({
    queryKey: ['levels'],
    queryFn: getLevels
  });

  // Filter levels to only show those assigned to the teacher
  const assignedLevels = levels.filter(level =>
    teacherProfile?.assigned_levels?.includes(level.id)
  );

  // Set default selected level if none is selected
  if (assignedLevels.length > 0 && !selectedLevel) {
    setSelectedLevel(assignedLevels[0].id);
  }

  // Fetch materials data
  const { data: materials = [], isLoading: isLoadingMaterials } = useQuery({
    queryKey: ['materials'],
    queryFn: getMaterials
  });

  // Create material mutation
  const createMaterialMutation = useMutation({
    mutationFn: createMaterial,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['materials'] });
      toast({
        title: "Success",
        description: "Material uploaded successfully",
      });
      resetForm();
      setIsAddDialogOpen(false);
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  // Delete material mutation
  const deleteMaterialMutation = useMutation({
    mutationFn: deleteMaterial,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['materials'] });
      toast({
        title: "Success",
        description: "Material deleted successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFormData(prev => ({ ...prev, file: e.target.files![0] }));
    }
  };

  const handleCreateMaterial = async () => {
    if (!formData.title || !formData.level_id || !formData.type) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    if (!formData.file) {
      toast({
        title: "Validation Error",
        description: "Please select a file to upload",
        variant: "destructive",
      });
      return;
    }

    setIsUploading(true);
    try {
      await createMaterialMutation.mutateAsync(formData);
    } catch (error) {
      console.error("Error creating material:", error);
    } finally {
      setIsUploading(false);
    }
  };

  const handleDeleteMaterial = async (materialId: string) => {
    try {
      await deleteMaterialMutation.mutateAsync(materialId);
    } catch (error) {
      console.error("Error deleting material:", error);
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      level_id: selectedLevel,
      type: 'document',
      file: undefined
    });
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Filter materials based on active tab
  const filteredMaterials = materials.filter(material => {
    if (activeTab === 'all') return true;
    if (activeTab === 'documents') return material.type === 'document';
    if (activeTab === 'worksheets') return material.type === 'worksheet';
    if (activeTab === 'presentations') return material.type === 'presentation';
    if (activeTab === 'media') return material.type === 'video' || material.type === 'audio';
    return true;
  });

  const getIconForType = (type: string) => {
    const materialType = MATERIAL_TYPES.find(t => t.value === type);
    if (!materialType) return <Folder className="h-6 w-6 text-primary" />;
    
    const Icon = materialType.icon;
    return <Icon className="h-6 w-6 text-primary" />;
  };

  const isLoading = isLoadingLevels || isLoadingMaterials;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Course Materials</h2>
          <p className="text-muted-foreground">
            Manage and share learning resources
          </p>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Upload Material
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Upload Course Material</DialogTitle>
              <DialogDescription>
                Add new learning resources for your students
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="title" className="text-right">
                  Title
                </Label>
                <Input
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="description" className="text-right">
                  Description
                </Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="level_id" className="text-right">
                  Level
                </Label>
                <Select
                  value={formData.level_id}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, level_id: value }))}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select a level" />
                  </SelectTrigger>
                  <SelectContent>
                    {assignedLevels.map(level => (
                      <SelectItem key={level.id} value={level.id}>
                        {level.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="type" className="text-right">
                  Type
                </Label>
                <Select
                  value={formData.type}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, type: value }))}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select material type" />
                  </SelectTrigger>
                  <SelectContent>
                    {MATERIAL_TYPES.map(type => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="file" className="text-right">
                  File
                </Label>
                <Input
                  id="file"
                  name="file"
                  type="file"
                  ref={fileInputRef}
                  onChange={handleFileChange}
                  className="col-span-3"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateMaterial} disabled={isUploading}>
                {isUploading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Upload Material
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="all">All Materials</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="worksheets">Worksheets</TabsTrigger>
          <TabsTrigger value="presentations">Presentations</TabsTrigger>
          <TabsTrigger value="media">Media</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4">
          {filteredMaterials.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
              <File className="h-12 w-12 mb-4" />
              <p>No materials found</p>
              <p className="text-sm mb-4">Upload materials to share with your students</p>
              <Button onClick={() => setIsAddDialogOpen(true)}>
                <Upload className="mr-2 h-4 w-4" />
                Upload Material
              </Button>
            </div>
          ) : (
            filteredMaterials.map((material) => {
              const level = levels.find(l => l.id === material.level_id);
              return (
                <Card key={material.id}>
                  <CardHeader className="flex flex-row items-start justify-between space-y-0">
                    <div className="flex items-start space-x-4">
                      <div className="bg-primary/10 p-2 rounded-lg">
                        {getIconForType(material.type)}
                      </div>
                      <div>
                        <CardTitle>{material.title}</CardTitle>
                        <CardDescription>{material.description}</CardDescription>
                        <div className="flex items-center space-x-4 mt-2 text-sm text-muted-foreground">
                          <span>Type: {material.type}</span>
                          <span>Size: {material.file_size ? `${(material.file_size / 1024 / 1024).toFixed(2)} MB` : 'Unknown'}</span>
                          <span>Level: {level?.name}</span>
                          <span>Added: {material.created_at ? new Date(material.created_at).toLocaleDateString() : 'Unknown'}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="icon" asChild>
                        <a href={material.file_url} target="_blank" rel="noopener noreferrer">
                          <Download className="h-4 w-4" />
                        </a>
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="outline" size="icon">
                            <Trash2 className="h-4 w-4 text-destructive" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Delete Material</AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to delete this material? This action cannot be undone.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleDeleteMaterial(material.id)}
                              className="bg-destructive text-destructive-foreground"
                            >
                              Delete
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </CardHeader>
                </Card>
              );
            })
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CourseMaterials; 