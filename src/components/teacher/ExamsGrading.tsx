import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { Loader2, Plus, FileText, Calendar, Clock, Trash2, GraduationCap } from 'lucide-react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getLevels } from '@/api/levels';
import { getExams, createExam, deleteExam, getStudentsForExam, bulkAddExamResults, ExamFormData, Exam } from '@/api/exams';
import { getSubjects } from '@/api/subjects';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { format } from 'date-fns';

// Extended user profile interface for teachers
interface TeacherProfile {
  assigned_levels?: string[];
  assigned_courses?: string[];
}

const ExamsGrading = () => {
  const { userProfile } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isGradingDialogOpen, setIsGradingDialogOpen] = useState(false);
  const [selectedExam, setSelectedExam] = useState<Exam | null>(null);
  const [studentGrades, setStudentGrades] = useState<{[key: string]: {marks: string, grade: string, remarks: string}}>({});

  // Cast userProfile to include teacher-specific properties
  const teacherProfile = userProfile as (typeof userProfile & TeacherProfile) | null;

  // Fetch levels data
  const { data: levels = [], isLoading: isLoadingLevels } = useQuery({
    queryKey: ['levels'],
    queryFn: getLevels
  });

  // Fetch subjects data
  const { data: subjects = [] } = useQuery({
    queryKey: ['subjects'],
    queryFn: getSubjects
  });

  // Note: Courses data available if needed for future features

  // Filter subjects based on teacher's assigned courses
  const teacherSubjects = subjects.filter(subject =>
    teacherProfile?.assigned_courses?.includes(subject.courseId)
  );

  // Filter levels to only show those assigned to the teacher
  const assignedLevels = levels.filter(level =>
    teacherProfile?.assigned_levels?.includes(level.id)
  );

  // Fetch exams data
  const { data: exams = [], isLoading: isLoadingExams } = useQuery({
    queryKey: ['exams'],
    queryFn: getExams
  });

  // Fetch students for selected exam
  const { data: students = [], isLoading: isLoadingStudents } = useQuery({
    queryKey: ['students', selectedExam?.id],
    queryFn: () => selectedExam ? getStudentsForExam(selectedExam.id) : Promise.resolve([]),
    enabled: !!selectedExam
  });

  // Create exam mutation
  const createExamMutation = useMutation({
    mutationFn: (data: ExamFormData) => createExam(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['exams'] });
      setIsAddDialogOpen(false);
      resetForm();
      toast({
        title: "Success",
        description: "Exam created successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create exam",
        variant: "destructive",
      });
    }
  });

  // Note: Update exam mutation available for future edit functionality

  // Delete exam mutation
  const deleteExamMutation = useMutation({
    mutationFn: (id: string) => deleteExam(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['exams'] });
      toast({
        title: "Success",
        description: "Exam deleted successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete exam",
        variant: "destructive",
      });
    }
  });

  // Add exam results mutation
  const addExamResultsMutation = useMutation({
    mutationFn: ({ examId, results }: { examId: string; results: { studentId: string; marks: number; grade: string; remarks?: string }[] }) => 
      bulkAddExamResults(examId, results),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['students', selectedExam?.id] });
      setIsGradingDialogOpen(false);
      toast({
        title: "Success",
        description: "Grades saved successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to save grades",
        variant: "destructive",
      });
    }
  });

  const [formData, setFormData] = useState({
    name: '',
    subject: '',
    level_id: '',
    course_id: '',
    type: '',
    date: '',
    time: '',
    class: ''
  });

  const resetForm = () => {
    setFormData({
      name: '',
      subject: '',
      level_id: '',
      course_id: '',
      type: '',
      date: '',
      time: '',
      class: ''
    });
  };

  const handleCreateExam = () => {
    if (!formData.name || !formData.level_id || !formData.date) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    createExamMutation.mutate(formData);
  };

  const handleDeleteExam = (examId: string) => {
    deleteExamMutation.mutate(examId);
  };

  const handleGradeExam = (exam: Exam) => {
    setSelectedExam(exam);
    setIsGradingDialogOpen(true);
    
    // Initialize student grades
    const initialGrades: {[key: string]: {marks: string, grade: string, remarks: string}} = {};
    students.forEach(student => {
      initialGrades[student.id] = {
        marks: student.examResult?.marks?.toString() || '',
        grade: student.examResult?.grade || '',
        remarks: student.examResult?.remarks || ''
      };
    });
    setStudentGrades(initialGrades);
  };

  const handleSaveGrades = () => {
    if (!selectedExam) return;
    
    const results = Object.entries(studentGrades).map(([studentId, data]) => ({
      studentId,
      marks: Number(data.marks) || 0,
      grade: data.grade || calculateGrade(Number(data.marks) || 0),
      remarks: data.remarks
    }));
    
    addExamResultsMutation.mutate({
      examId: selectedExam.id,
      results
    });
  };
  
  const calculateGrade = (marks: number): string => {
    if (marks >= 90) return 'A+';
    if (marks >= 80) return 'A';
    if (marks >= 70) return 'B';
    if (marks >= 60) return 'C';
    if (marks >= 50) return 'D';
    return 'F';
  };
  
  const handleGradeChange = (studentId: string, field: 'marks' | 'grade' | 'remarks', value: string) => {
    setStudentGrades(prev => ({
      ...prev,
      [studentId]: {
        ...prev[studentId],
        [field]: value,
        // Auto-calculate grade if marks change
        ...(field === 'marks' ? { grade: calculateGrade(Number(value) || 0) } : {})
      }
    }));
  };

  if (isLoadingLevels || isLoadingExams) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  // Filter exams to only show those for assigned levels
  const filteredExams = exams.filter(exam => 
    assignedLevels.some(level => level.id === exam.level_id)
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Exams & Grading</h2>
          <p className="text-muted-foreground">
            Create and grade exams for your classes
          </p>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Exam
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Exam</DialogTitle>
              <DialogDescription>
                Set up a new exam for your students
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Name
                </Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="subject" className="text-right">
                  Subject
                </Label>
                <Select
                  value={formData.subject}
                  onValueChange={(value) => {
                    const selectedSubject = subjects.find(s => s.id === value);
                    setFormData(prev => ({
                      ...prev,
                      subject: value,
                      course_id: selectedSubject?.courseId || ''
                    }));
                  }}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select a subject" />
                  </SelectTrigger>
                  <SelectContent>
                    {teacherSubjects.map(subject => (
                      <SelectItem key={subject.id} value={subject.id}>
                        {subject.name} ({subject.code})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="level" className="text-right">
                  Level
                </Label>
                <Select
                  value={formData.level_id}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, level_id: value }))}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select a level" />
                  </SelectTrigger>
                  <SelectContent>
                    {assignedLevels.map(level => (
                      <SelectItem key={level.id} value={level.id}>
                        {level.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="type" className="text-right">
                  Type
                </Label>
                <Select
                  value={formData.type}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, type: value }))}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select exam type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="midterm">Midterm</SelectItem>
                    <SelectItem value="final">Final</SelectItem>
                    <SelectItem value="quiz">Quiz</SelectItem>
                    <SelectItem value="test">Test</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="date" className="text-right">
                  Date
                </Label>
                <Input
                  id="date"
                  type="date"
                  value={formData.date}
                  onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="time" className="text-right">
                  Time
                </Label>
                <Input
                  id="time"
                  type="time"
                  value={formData.time}
                  onChange={(e) => setFormData(prev => ({ ...prev, time: e.target.value }))}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="class" className="text-right">
                  Class
                </Label>
                <Input
                  id="class"
                  value={formData.class}
                  onChange={(e) => setFormData(prev => ({ ...prev, class: e.target.value }))}
                  className="col-span-3"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                Cancel
              </Button>
              <Button 
                onClick={handleCreateExam}
                disabled={createExamMutation.isPending}
              >
                {createExamMutation.isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Creating...
                  </>
                ) : (
                  'Create Exam'
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid gap-4">
        {filteredExams.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
            <FileText className="h-12 w-12 mb-4" />
            <p>No exams created yet</p>
            <p className="text-sm">Create your first exam to get started</p>
          </div>
        ) : (
          filteredExams.map((exam) => {
            const level = levels.find(l => l.id === exam.level_id);
            const subject = subjects.find(s => s.id === exam.subject);
            return (
              <Card key={exam.id}>
                <CardHeader className="flex flex-row items-start justify-between space-y-0">
                  <div>
                    <CardTitle className="flex items-center">
                      <FileText className="h-5 w-5 mr-2" />
                      {exam.name}
                    </CardTitle>
                    <CardDescription>
                      {subject ? `${subject.name} (${subject.code})` : exam.subject}
                    </CardDescription>
                  </div>
                  <div className="flex space-x-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleGradeExam(exam)}
                    >
                      <GraduationCap className="h-4 w-4 mr-2" />
                      Grade
                    </Button>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="outline" size="icon">
                          <Trash2 className="h-4 w-4 text-destructive" />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Delete Exam</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to delete this exam? This action cannot be undone.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleDeleteExam(exam.id)}
                            className="bg-destructive text-destructive-foreground"
                          >
                            Delete
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-col space-y-2">
                    <div className="flex items-center text-sm">
                      <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                      <span>Subject: {subject?.name || 'Unknown Subject'} ({subject?.code || 'N/A'})</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                      <span>Date: {format(new Date(exam.date), 'PPP')}</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                      <span>Time: {exam.time}</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <GraduationCap className="h-4 w-4 mr-2 text-muted-foreground" />
                      <span>Level: {level?.name || 'Unknown Level'}</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <span className="px-2 py-1 text-xs rounded-full bg-primary/10 text-primary">
                        {exam.status?.charAt(0).toUpperCase() + exam.status?.slice(1) || 'Upcoming'}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })
        )}
      </div>

      <Dialog open={isGradingDialogOpen} onOpenChange={setIsGradingDialogOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Grade Exam: {selectedExam?.name}</DialogTitle>
            <DialogDescription>
              Enter grades for each student
            </DialogDescription>
          </DialogHeader>
          {isLoadingStudents ? (
            <div className="flex items-center justify-center h-64">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <>
              <div className="max-h-[60vh] overflow-y-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Student Name</TableHead>
                      <TableHead>Marks</TableHead>
                      <TableHead>Grade</TableHead>
                      <TableHead>Remarks</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {students.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={4} className="text-center">
                          No students found for this exam
                        </TableCell>
                      </TableRow>
                    ) : (
                      students.map((student) => (
                        <TableRow key={student.id}>
                          <TableCell>{student.name}</TableCell>
                          <TableCell>
                            <Input
                              type="number"
                              value={studentGrades[student.id]?.marks || ''}
                              onChange={(e) => handleGradeChange(student.id, 'marks', e.target.value)}
                              className="w-20"
                            />
                          </TableCell>
                          <TableCell>
                            <Select
                              value={studentGrades[student.id]?.grade || ''}
                              onValueChange={(value) => handleGradeChange(student.id, 'grade', value)}
                            >
                              <SelectTrigger className="w-20">
                                <SelectValue placeholder="Grade" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="A+">A+</SelectItem>
                                <SelectItem value="A">A</SelectItem>
                                <SelectItem value="B">B</SelectItem>
                                <SelectItem value="C">C</SelectItem>
                                <SelectItem value="D">D</SelectItem>
                                <SelectItem value="F">F</SelectItem>
                              </SelectContent>
                            </Select>
                          </TableCell>
                          <TableCell>
                            <Input
                              value={studentGrades[student.id]?.remarks || ''}
                              onChange={(e) => handleGradeChange(student.id, 'remarks', e.target.value)}
                              className="w-full"
                            />
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsGradingDialogOpen(false)}>
                  Cancel
                </Button>
                <Button 
                  onClick={handleSaveGrades}
                  disabled={addExamResultsMutation.isPending || students.length === 0}
                >
                  {addExamResultsMutation.isPending ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    'Save Grades'
                  )}
                </Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ExamsGrading; 